# AzerothCore _averageUpdateTime 变量完整数据流追踪分析

## 1. 概述

本文档对AzerothCore项目中`_averageUpdateTime`变量进行完整的数据流追踪分析，从游戏服务器启动到平均更新时间计算的整个过程。

## 2. 核心调用链路图

### 2.1 主要调用链路
```
main() [Main.cpp:400]
  └── WorldUpdateLoop() [Main.cpp:583]
      └── sWorld->Update(diff) [Main.cpp:615]
          └── sWorldUpdateTime.UpdateWithDiff(diff) [World.cpp:2072]
              └── _averageUpdateTime = _totalUpdateTime / _updateTimeDataTable.size() [UpdateTime.cpp:122]
```

### 2.2 时间差值计算链路
```
WorldUpdateLoop() [Main.cpp:583]
  ├── realPrevTime = getMSTime() [Main.cpp:587]
  ├── realCurrTime = getMSTime() [Main.cpp:602]
  └── diff = getMSTimeDiff(realPrevTime, realCurrTime) [Main.cpp:604]
      └── getMSTimeDiff() [Timer.h:110-121]
          └── steady_clock::now() - GetApplicationStartTime() [Timer.h:84-89]
```

## 3. 关键类和变量分析

### 3.1 UpdateTime类结构
```cpp
class UpdateTime {
private:
    DiffTableArray _updateTimeDataTable;     // 500个样本的循环缓冲区
    uint32 _averageUpdateTime;               // 平均更新时间（目标变量）
    uint32 _totalUpdateTime;                 // 总更新时间
    uint32 _updateTimeTableIndex;            // 当前索引位置
    uint32 _maxUpdateTime;                   // 最大更新时间
    uint32 _maxUpdateTimeOfLastTable;        // 上一轮最大时间
    uint32 _maxUpdateTimeOfCurrentTable;     // 当前轮最大时间
    DiffTableArray _orderedUpdateTimeDataTable; // 排序后的数据表
    bool _needsReorder;                      // 是否需要重新排序
};
```

### 3.2 WorldUpdateTime类扩展
```cpp
class WorldUpdateTime : public UpdateTime {
private:
    Milliseconds _recordUpdateTimeInverval;  // 记录间隔（默认300000ms = 5分钟）
    Milliseconds _recordUpdateTimeMin;       // 最小记录阈值（默认100ms）
    Milliseconds _lastRecordTime;            // 上次记录时间
};
```

## 4. 配置参数影响分析

### 4.1 核心配置参数
| 参数名 | 默认值 | 作用 | 影响 |
|--------|--------|------|------|
| MinWorldUpdateTime | 1ms | 最小更新间隔 | 控制游戏循环频率 |
| MaxCoreStuckTime | 60s | 核心卡死检测时间 | 防止服务器假死 |
| RecordUpdateTimeDiffInterval | 300000ms | 日志记录间隔 | 控制性能日志频率 |
| MinRecordUpdateTimeDiff | 100ms | 最小记录阈值 | 过滤小的时间差值 |
| ThreadPool | 2 | 线程池大小 | 影响并发处理能力 |

### 4.2 配置参数对更新时间的影响
- **MinWorldUpdateTime**: 设置过小会导致CPU占用过高，设置过大会影响游戏响应性
- **MaxCoreStuckTime**: 用于检测服务器卡死，不直接影响更新时间计算
- **RecordUpdateTimeDiffInterval**: 控制性能日志输出频率，不影响计算逻辑
- **ThreadPool**: 影响网络IO和数据库操作的并发性能

## 5. 历史数据管理机制

### 5.1 循环缓冲区设计
```cpp
constexpr auto AVG_DIFF_COUNT = 500;  // 固定500个样本
using DiffTableArray = std::array<uint32, AVG_DIFF_COUNT>;
```

### 5.2 数据更新逻辑
```cpp
void UpdateTime::UpdateWithDiff(uint32 diff) {
    // 1. 标记需要重新排序
    _needsReorder = true;
    
    // 2. 更新总时间：减去旧值，加上新值
    _totalUpdateTime = _totalUpdateTime - _updateTimeDataTable[_updateTimeTableIndex] + diff;
    
    // 3. 存储新的时间差值
    _updateTimeDataTable[_updateTimeTableIndex] = diff;
    
    // 4. 更新最大值统计
    if (diff > _maxUpdateTime)
        _maxUpdateTime = diff;
    
    // 5. 循环索引管理
    if (++_updateTimeTableIndex >= _updateTimeDataTable.size()) {
        _updateTimeTableIndex = 0;
        _maxUpdateTimeOfLastTable = _maxUpdateTimeOfCurrentTable;
        _maxUpdateTimeOfCurrentTable = 0;
    }
    
    // 6. 计算平均值
    if (_updateTimeDataTable[_updateTimeDataTable.size() - 1])
        _averageUpdateTime = _totalUpdateTime / _updateTimeDataTable.size();
    else if (_updateTimeTableIndex)
        _averageUpdateTime = _totalUpdateTime / _updateTimeTableIndex;
}
```

## 6. 多线程处理机制

### 6.1 MapUpdater线程池
```cpp
class MapUpdater {
private:
    std::vector<std::thread> _workerThreads;     // 工作线程池
    ProducerConsumerQueue<UpdateRequest*> _queue; // 任务队列
    std::atomic<std::size_t> pending_requests;   // 待处理请求数
    std::condition_variable _condition;          // 条件变量
    std::mutex _lock;                           // 互斥锁
};
```

### 6.2 地图更新调度
```cpp
// 主线程调度地图更新
if (m_updater.activated())
    m_updater.schedule_update(*iter->second, t_diff, s_diff);
else
    iter->second->Update(t_diff, s_diff);

// 等待所有地图更新完成
if (m_updater.activated())
    m_updater.wait();
```

### 6.3 多线程对更新时间的影响
- **启用多线程**: 地图更新并行执行，总体更新时间可能减少
- **禁用多线程**: 地图按顺序更新，更新时间可能增加
- **线程同步开销**: wait()操作会等待所有线程完成，可能增加延迟

## 7. 性能瓶颈点识别

### 7.1 World::Update中的主要性能消耗点
```cpp
void World::Update(uint32 diff) {
    // 高频率执行的组件
    METRIC_TIMER("world_update_time", METRIC_TAG("type", "Update sessions"));
    sWorldSessionMgr->UpdateSessions(diff);  // 玩家会话更新
    
    METRIC_TIMER("world_update_time", METRIC_TAG("type", "Update maps"));
    sMapMgr->Update(diff);  // 地图更新（最大瓶颈）
    
    METRIC_TIMER("world_update_time", METRIC_TAG("type", "Update battlegrounds"));
    sBattlegroundMgr->Update(diff);  // 战场更新
    
    // 低频率但可能耗时的操作
    if (_timers[WUPDATE_UPTIME].Passed()) {
        LoginDatabase.Execute(stmt);  // 数据库操作
    }
}
```

### 7.2 潜在性能瓶颈
1. **地图更新 (sMapMgr->Update)**: 最大的性能消耗点
2. **玩家会话更新**: 随在线玩家数量线性增长
3. **数据库操作**: 可能导致IO阻塞
4. **战场和副本更新**: 复杂的游戏逻辑处理
5. **脚本系统更新**: 自定义脚本的执行开销

## 8. 日志记录机制

### 8.1 性能日志触发条件
```cpp
void WorldUpdateTime::RecordUpdateTime(Milliseconds gameTimeMs, uint32 diff, uint32 sessionCount) {
    // 条件1: 记录间隔大于0且时间差大于最小阈值
    if (_recordUpdateTimeInverval > 0s && diff > _recordUpdateTimeMin.count()) {
        // 条件2: 距离上次记录时间超过设定间隔
        if (GetMSTimeDiff(_lastRecordTime, gameTimeMs) > _recordUpdateTimeInverval) {
            // 输出详细的性能统计信息
            LOG_INFO("time.update", "Update time diff: {}ms with {} players online", 
                     GetLastUpdateTime(), sessionCount);
            LOG_INFO("time.update", "Last {} diffs summary:", GetDatasetSize());
            LOG_INFO("time.update", "|- Mean: {}ms", GetAverageUpdateTime());
            LOG_INFO("time.update", "|- Median: {}ms", GetPercentile(50));
            LOG_INFO("time.update", "|- Percentiles (95, 99, max): {}ms, {}ms, {}ms", 
                     GetPercentile(95), GetPercentile(99), GetPercentile(100));
        }
    }
}
```

### 8.2 性能指标解读
- **Mean (平均值)**: `_averageUpdateTime`的值，反映整体性能
- **Median (中位数)**: 50%分位数，更稳定的性能指标
- **95/99分位数**: 反映性能峰值情况
- **Max**: 最大更新时间，识别性能尖峰

## 9. 时间测量精度分析

### 9.1 时间获取机制
```cpp
inline uint32 getMSTime() {
    using namespace std::chrono;
    return uint32(duration_cast<milliseconds>(
        steady_clock::now() - GetApplicationStartTime()).count());
}
```

### 9.2 时间差值计算
```cpp
inline uint32 getMSTimeDiff(uint32 oldMSTime, uint32 newMSTime) {
    // 处理时间溢出情况
    if (oldMSTime > newMSTime) {
        return (0xFFFFFFFF - oldMSTime) + newMSTime;
    } else {
        return newMSTime - oldMSTime;
    }
}
```

### 9.3 精度特性
- **时间源**: `std::chrono::steady_clock`，单调递增时钟
- **精度**: 毫秒级精度
- **溢出处理**: 支持uint32溢出的正确处理
- **稳定性**: 不受系统时间调整影响

## 10. 根本触发机制

### 10.1 服务器启动流程
```
main() 
  └── 初始化各种管理器
  └── 启动线程池
  └── WorldUpdateLoop()  // 进入主游戏循环
```

### 10.2 游戏主循环
```cpp
while (!World::IsStopped()) {
    ++World::m_worldLoopCounter;
    realCurrTime = getMSTime();
    
    uint32 diff = getMSTimeDiff(realPrevTime, realCurrTime);
    
    // 最小更新时间控制
    if (diff < minUpdateDiff) {
        std::this_thread::sleep_for(Milliseconds(sleepTime));
        continue;
    }
    
    sWorld->Update(diff);  // 核心更新调用
    realPrevTime = realCurrTime;
}
```

### 10.3 更新频率控制
- **目标频率**: 约1000Hz（1ms间隔）
- **实际频率**: 受系统负载和配置影响
- **自适应调节**: 根据处理时间动态调整

## 11. 总结

`_averageUpdateTime`变量的数据流追踪揭示了AzerothCore服务器性能监控的完整机制：

1. **数据源**: 游戏主循环的时间差值测量
2. **存储机制**: 500个样本的循环缓冲区
3. **计算方法**: 简单算术平均值
4. **更新频率**: 每个游戏帧更新一次
5. **监控输出**: 定期记录详细的性能统计信息

这个系统为服务器性能监控和优化提供了重要的数据基础。

## 12. 详细函数调用关系图

### 12.1 完整调用栈
```
程序入口: main() [worldserver/Main.cpp:427]
├── 服务器初始化
├── 线程池启动 [Main.cpp:236-257]
├── WorldUpdateLoop() [Main.cpp:400]
│   ├── 配置加载: minUpdateDiff = sConfigMgr->GetOption<int32>("MinWorldUpdateTime", 1)
│   ├── 时间初始化: realPrevTime = getMSTime() [Timer.h:103-108]
│   └── 主循环: while (!World::IsStopped())
│       ├── 时间测量: realCurrTime = getMSTime()
│       ├── 差值计算: diff = getMSTimeDiff(realPrevTime, realCurrTime) [Timer.h:110-121]
│       ├── 频率控制: if (diff < minUpdateDiff) sleep()
│       ├── 核心更新: sWorld->Update(diff) [World.cpp:2064]
│       │   ├── 性能监控: METRIC_TIMER("world_update_time_total")
│       │   ├── 游戏时间更新: _UpdateGameTime() [World.cpp:2392-2398]
│       │   ├── 更新时间统计: sWorldUpdateTime.UpdateWithDiff(diff) [UpdateTime.cpp:102]
│       │   │   ├── 循环缓冲区更新
│       │   │   ├── 总时间重计算: _totalUpdateTime = _totalUpdateTime - old + diff
│       │   │   ├── 最大值更新: if (diff > _maxUpdateTime) _maxUpdateTime = diff
│       │   │   ├── 索引循环: _updateTimeTableIndex++
│       │   │   └── 平均值计算: _averageUpdateTime = _totalUpdateTime / size
│       │   ├── 性能日志: sWorldUpdateTime.RecordUpdateTime() [UpdateTime.cpp:163]
│       │   ├── 各子系统更新:
│       │   │   ├── DynamicVisibilityMgr::Update()
│       │   │   ├── 定时器更新: _timers[i].Update(diff)
│       │   │   ├── 会话更新: sWorldSessionMgr->UpdateSessions(diff)
│       │   │   ├── 地图更新: sMapMgr->Update(diff) [MapMgr.cpp:269]
│       │   │   │   ├── 多线程调度: m_updater.schedule_update()
│       │   │   │   ├── 地图遍历: for (iter = i_maps.begin(); ...)
│       │   │   │   ├── 单地图更新: iter->second->Update(t_diff, s_diff)
│       │   │   │   └── 同步等待: m_updater.wait()
│       │   │   ├── 战场更新: sBattlegroundMgr->Update(diff)
│       │   │   ├── 脚本更新: sScriptMgr->OnWorldUpdate(diff)
│       │   │   └── 指标更新: sMetric->Update()
│       │   └── 性能指标记录: METRIC_VALUE("update_time_diff", diff)
│       └── 时间更新: realPrevTime = realCurrTime
└── 服务器关闭清理
```

### 12.2 关键数据结构交互
```
UpdateTime类实例: sWorldUpdateTime
├── 数据成员:
│   ├── _updateTimeDataTable[500]: 循环缓冲区存储最近500次更新时间
│   ├── _totalUpdateTime: 当前缓冲区内所有时间的总和
│   ├── _updateTimeTableIndex: 当前写入位置索引 (0-499循环)
│   ├── _averageUpdateTime: 计算得出的平均更新时间 ⭐目标变量
│   ├── _maxUpdateTime: 历史最大更新时间
│   └── _orderedUpdateTimeDataTable[500]: 用于百分位数计算的排序数组
├── 核心方法:
│   ├── UpdateWithDiff(uint32 diff): 更新统计数据
│   ├── GetAverageUpdateTime(): 返回 _averageUpdateTime
│   ├── GetPercentile(uint8 p): 计算百分位数
│   └── RecordUpdateTime(): 条件性日志输出
└── 配置参数:
    ├── RecordUpdateTimeDiffInterval: 日志记录间隔
    └── MinRecordUpdateTimeDiff: 最小记录阈值
```

## 13. 性能影响因素深度分析

### 13.1 直接影响因素
```cpp
// 1. 在线玩家数量影响
sWorldSessionMgr->UpdateSessions(diff);  // O(n) 复杂度，n=在线玩家数

// 2. 活跃地图数量影响
for (iter = i_maps.begin(); iter != i_maps.end(); ++iter) {
    iter->second->Update(t_diff, s_diff);  // 每个地图的更新开销
}

// 3. 数据库操作影响
if (_timers[WUPDATE_UPTIME].Passed()) {
    LoginDatabase.Execute(stmt);  // 可能的IO阻塞
}

// 4. 脚本系统影响
sScriptMgr->OnWorldUpdate(diff);  // 自定义脚本执行时间
```

### 13.2 间接影响因素
- **系统负载**: CPU、内存、磁盘IO使用率
- **网络状况**: 玩家连接质量和数量
- **数据库性能**: 查询响应时间和连接池状态
- **内存碎片**: 长时间运行后的内存分配效率
- **线程竞争**: 多线程环境下的锁竞争

### 13.3 配置优化建议
```ini
# 高性能配置示例
MinWorldUpdateTime = 1          # 保持最高响应性
ThreadPool = 4                  # 根据CPU核心数调整
RecordUpdateTimeDiffInterval = 60000  # 更频繁的性能监控
MinRecordUpdateTimeDiff = 50    # 更敏感的异常检测

# 稳定性优先配置
MinWorldUpdateTime = 10         # 降低CPU压力
ThreadPool = 2                  # 减少线程竞争
MaxCoreStuckTime = 30           # 更快的卡死检测
```

## 14. 故障诊断指南

### 14.1 性能问题识别
```
更新时间阈值参考:
- < 50ms:  优秀性能
- 50-100ms: 良好性能
- 100-300ms: 可接受性能
- 300-600ms: 性能警告
- > 600ms:  性能严重问题
```

### 14.2 常见问题和解决方案
1. **平均更新时间持续过高**
   - 检查在线玩家数量是否超出服务器承载能力
   - 分析METRIC_TIMER日志确定瓶颈组件
   - 考虑启用多线程地图更新

2. **更新时间波动剧烈**
   - 检查数据库连接和查询性能
   - 分析是否有定时任务在高峰期执行
   - 检查内存使用情况和垃圾回收

3. **特定时间段性能下降**
   - 分析游戏内事件和玩家活动模式
   - 检查定时任务的执行时间安排
   - 监控系统资源使用情况

## 15. 扩展监控建议

### 15.1 增强监控指标
```cpp
// 建议添加的监控点
METRIC_TIMER("world_update_component", METRIC_TAG("component", "sessions"));
METRIC_TIMER("world_update_component", METRIC_TAG("component", "maps"));
METRIC_TIMER("world_update_component", METRIC_TAG("component", "database"));
METRIC_VALUE("active_players_count", sessionCount);
METRIC_VALUE("active_maps_count", mapCount);
```

### 15.2 告警机制建议
- 当平均更新时间超过300ms时触发警告
- 当99分位数超过1000ms时触发严重告警
- 监控更新时间的变化趋势，检测性能退化

这个完整的分析为AzerothCore服务器的性能监控、问题诊断和优化提供了全面的技术基础。

## 16. 监控系统扩展方案

### 16.1 增强现有监控指标

#### 16.1.1 细粒度组件监控
```cpp
// 扩展UpdateTime类，添加组件级监控
class EnhancedUpdateTime : public UpdateTime {
private:
    std::unordered_map<std::string, ComponentMetrics> _componentMetrics;

public:
    struct ComponentMetrics {
        uint32 totalTime = 0;
        uint32 averageTime = 0;
        uint32 maxTime = 0;
        uint32 callCount = 0;
        std::array<uint32, 100> recentTimes; // 最近100次调用
        uint32 recentIndex = 0;
    };

    void UpdateComponentTime(const std::string& component, uint32 diff) {
        auto& metrics = _componentMetrics[component];
        metrics.totalTime += diff;
        metrics.callCount++;
        metrics.averageTime = metrics.totalTime / metrics.callCount;

        if (diff > metrics.maxTime) {
            metrics.maxTime = diff;
        }

        metrics.recentTimes[metrics.recentIndex] = diff;
        metrics.recentIndex = (metrics.recentIndex + 1) % 100;

        // 发送到监控系统
        METRIC_VALUE("component_update_time", diff,
                    {{"component", component}, {"realm", _realmName}});
    }

    ComponentMetrics GetComponentMetrics(const std::string& component) const {
        auto it = _componentMetrics.find(component);
        return it != _componentMetrics.end() ? it->second : ComponentMetrics{};
    }
};
```

#### 16.1.2 实时告警系统
```cpp
class PerformanceAlertManager {
private:
    struct AlertThreshold {
        uint32 warningThreshold;
        uint32 criticalThreshold;
        uint32 consecutiveCount;
        std::chrono::steady_clock::time_point lastAlert;
        uint32 alertCooldown; // 秒
    };

    std::unordered_map<std::string, AlertThreshold> _thresholds;
    std::unordered_map<std::string, uint32> _consecutiveViolations;

public:
    void RegisterThreshold(const std::string& metric, uint32 warning, uint32 critical, uint32 cooldown = 300) {
        _thresholds[metric] = {warning, critical, 0, {}, cooldown};
    }

    void CheckMetric(const std::string& metric, uint32 value) {
        auto it = _thresholds.find(metric);
        if (it == _thresholds.end()) return;

        auto& threshold = it->second;
        auto now = std::chrono::steady_clock::now();

        if (value >= threshold.criticalThreshold) {
            _consecutiveViolations[metric]++;
            if (_consecutiveViolations[metric] >= 3 &&
                std::chrono::duration_cast<std::chrono::seconds>(now - threshold.lastAlert).count() > threshold.alertCooldown) {

                SendCriticalAlert(metric, value);
                threshold.lastAlert = now;
                _consecutiveViolations[metric] = 0;
            }
        } else if (value >= threshold.warningThreshold) {
            SendWarningAlert(metric, value);
        } else {
            _consecutiveViolations[metric] = 0;
        }
    }

private:
    void SendCriticalAlert(const std::string& metric, uint32 value) {
        LOG_ERROR("performance.alert", "CRITICAL: {} = {}ms exceeds threshold", metric, value);
        METRIC_EVENT("performance_alert", "Critical Performance Alert",
                    fmt::format("{} = {}ms", metric, value));

        // 可以集成外部告警系统（如邮件、Slack、钉钉等）
        NotifyExternalSystems("CRITICAL", metric, value);
    }

    void SendWarningAlert(const std::string& metric, uint32 value) {
        LOG_WARN("performance.alert", "WARNING: {} = {}ms exceeds warning threshold", metric, value);
        METRIC_EVENT("performance_alert", "Performance Warning",
                    fmt::format("{} = {}ms", metric, value));
    }

    void NotifyExternalSystems(const std::string& level, const std::string& metric, uint32 value) {
        // 实现外部通知逻辑
        // 例如：HTTP POST到监控系统、发送邮件等
    }
};
```

#### 16.1.3 百分位数统计增强
```cpp
class AdvancedPercentileCalculator {
private:
    std::vector<uint32> _sortedData;
    bool _needsSort = false;

public:
    void AddValue(uint32 value) {
        _sortedData.push_back(value);
        _needsSort = true;
    }

    std::map<uint32, uint32> GetPercentiles(const std::vector<uint32>& percentiles) {
        if (_needsSort) {
            std::sort(_sortedData.begin(), _sortedData.end());
            _needsSort = false;
        }

        std::map<uint32, uint32> results;
        for (uint32 p : percentiles) {
            results[p] = CalculatePercentile(p);
        }
        return results;
    }

    // 计算标准差
    double GetStandardDeviation() {
        if (_sortedData.empty()) return 0.0;

        double mean = GetMean();
        double sum = 0.0;
        for (uint32 value : _sortedData) {
            sum += std::pow(value - mean, 2);
        }
        return std::sqrt(sum / _sortedData.size());
    }

    // 检测异常值（使用IQR方法）
    std::vector<uint32> DetectOutliers() {
        auto q1 = CalculatePercentile(25);
        auto q3 = CalculatePercentile(75);
        auto iqr = q3 - q1;
        auto lowerBound = q1 - 1.5 * iqr;
        auto upperBound = q3 + 1.5 * iqr;

        std::vector<uint32> outliers;
        for (uint32 value : _sortedData) {
            if (value < lowerBound || value > upperBound) {
                outliers.push_back(value);
            }
        }
        return outliers;
    }

private:
    uint32 CalculatePercentile(uint32 percentile) {
        if (_sortedData.empty()) return 0;

        double index = (percentile / 100.0) * (_sortedData.size() - 1);
        uint32 lowerIndex = static_cast<uint32>(std::floor(index));
        uint32 upperIndex = static_cast<uint32>(std::ceil(index));

        if (lowerIndex == upperIndex) {
            return _sortedData[lowerIndex];
        }

        double weight = index - lowerIndex;
        return static_cast<uint32>(_sortedData[lowerIndex] * (1 - weight) + _sortedData[upperIndex] * weight);
    }

    double GetMean() {
        if (_sortedData.empty()) return 0.0;

        uint64 sum = 0;
        for (uint32 value : _sortedData) {
            sum += value;
        }
        return static_cast<double>(sum) / _sortedData.size();
    }
};
```

### 16.2 集成外部监控系统

#### 16.2.1 Prometheus集成
```cpp
class PrometheusExporter {
private:
    std::string _metricsEndpoint;
    std::unordered_map<std::string, double> _gauges;
    std::unordered_map<std::string, uint64> _counters;
    std::unordered_map<std::string, std::vector<double>> _histograms;

public:
    void Initialize(const std::string& endpoint) {
        _metricsEndpoint = endpoint;

        // 注册核心指标
        RegisterGauge("azerothcore_update_time_average", "Average world update time in milliseconds");
        RegisterGauge("azerothcore_players_online", "Number of players currently online");
        RegisterCounter("azerothcore_update_cycles_total", "Total number of world update cycles");
        RegisterHistogram("azerothcore_update_time_distribution", "Distribution of world update times");
    }

    void UpdateGauge(const std::string& name, double value) {
        _gauges[name] = value;
    }

    void IncrementCounter(const std::string& name, uint64 increment = 1) {
        _counters[name] += increment;
    }

    void RecordHistogram(const std::string& name, double value) {
        _histograms[name].push_back(value);

        // 保持最近1000个样本
        if (_histograms[name].size() > 1000) {
            _histograms[name].erase(_histograms[name].begin());
        }
    }

    std::string GenerateMetrics() {
        std::stringstream ss;

        // 输出Gauge指标
        for (const auto& [name, value] : _gauges) {
            ss << "# TYPE " << name << " gauge\n";
            ss << name << " " << value << "\n";
        }

        // 输出Counter指标
        for (const auto& [name, value] : _counters) {
            ss << "# TYPE " << name << " counter\n";
            ss << name << " " << value << "\n";
        }

        // 输出Histogram指标
        for (const auto& [name, values] : _histograms) {
            if (values.empty()) continue;

            ss << "# TYPE " << name << " histogram\n";

            // 计算分位数
            auto sortedValues = values;
            std::sort(sortedValues.begin(), sortedValues.end());

            std::vector<double> quantiles = {0.5, 0.9, 0.95, 0.99};
            for (double q : quantiles) {
                size_t index = static_cast<size_t>(q * (sortedValues.size() - 1));
                ss << name << "_bucket{le=\"" << sortedValues[index] << "\"} " <<
                      static_cast<size_t>(q * sortedValues.size()) << "\n";
            }

            ss << name << "_bucket{le=\"+Inf\"} " << sortedValues.size() << "\n";
            ss << name << "_count " << sortedValues.size() << "\n";
            ss << name << "_sum " << std::accumulate(sortedValues.begin(), sortedValues.end(), 0.0) << "\n";
        }

        return ss.str();
    }

private:
    void RegisterGauge(const std::string& name, const std::string& help) {
        _gauges[name] = 0.0;
    }

    void RegisterCounter(const std::string& name, const std::string& help) {
        _counters[name] = 0;
    }

    void RegisterHistogram(const std::string& name, const std::string& help) {
        _histograms[name] = {};
    }
};
```

#### 16.2.2 Grafana仪表板配置
```json
{
  "dashboard": {
    "title": "AzerothCore Performance Monitoring",
    "panels": [
      {
        "title": "World Update Time",
        "type": "graph",
        "targets": [
          {
            "expr": "azerothcore_update_time_average",
            "legendFormat": "Average Update Time"
          },
          {
            "expr": "histogram_quantile(0.95, azerothcore_update_time_distribution)",
            "legendFormat": "95th Percentile"
          }
        ],
        "yAxes": [
          {
            "label": "Time (ms)",
            "min": 0
          }
        ],
        "alert": {
          "conditions": [
            {
              "query": {
                "queryType": "",
                "refId": "A"
              },
              "reducer": {
                "type": "last",
                "params": []
              },
              "evaluator": {
                "params": [300],
                "type": "gt"
              }
            }
          ],
          "executionErrorState": "alerting",
          "noDataState": "no_data",
          "frequency": "10s",
          "handler": 1,
          "name": "High Update Time Alert",
          "message": "World update time is above 300ms"
        }
      },
      {
        "title": "Online Players",
        "type": "singlestat",
        "targets": [
          {
            "expr": "azerothcore_players_online",
            "legendFormat": "Players"
          }
        ]
      },
      {
        "title": "Component Performance Breakdown",
        "type": "graph",
        "targets": [
          {
            "expr": "azerothcore_component_update_time",
            "legendFormat": "{{component}}"
          }
        ]
      }
    ]
  }
}
```

### 16.3 智能分析功能

#### 16.3.1 性能趋势预测
```cpp
class PerformanceTrendAnalyzer {
private:
    struct TrendData {
        std::vector<std::pair<uint64, double>> timeSeriesData; // timestamp, value
        double slope = 0.0;
        double intercept = 0.0;
        double correlation = 0.0;
        uint64 lastAnalysisTime = 0;
    };

    std::unordered_map<std::string, TrendData> _trends;

public:
    void AddDataPoint(const std::string& metric, double value) {
        auto& trend = _trends[metric];
        uint64 timestamp = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        trend.timeSeriesData.emplace_back(timestamp, value);

        // 保持最近24小时的数据
        uint64 cutoffTime = timestamp - 86400; // 24小时前
        trend.timeSeriesData.erase(
            std::remove_if(trend.timeSeriesData.begin(), trend.timeSeriesData.end(),
                [cutoffTime](const auto& point) { return point.first < cutoffTime; }),
            trend.timeSeriesData.end());

        // 每小时重新分析一次趋势
        if (timestamp - trend.lastAnalysisTime > 3600) {
            AnalyzeTrend(metric);
            trend.lastAnalysisTime = timestamp;
        }
    }

    struct PredictionResult {
        double predictedValue;
        double confidence;
        std::string trend; // "improving", "stable", "degrading"
        uint64 predictionTime;
    };

    PredictionResult PredictValue(const std::string& metric, uint64 futureSeconds) {
        auto it = _trends.find(metric);
        if (it == _trends.end() || it->second.timeSeriesData.empty()) {
            return {0.0, 0.0, "unknown", 0};
        }

        const auto& trend = it->second;
        uint64 currentTime = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        uint64 futureTime = currentTime + futureSeconds;

        double predictedValue = trend.slope * futureTime + trend.intercept;

        // 计算置信度（基于相关系数）
        double confidence = std::abs(trend.correlation);

        // 判断趋势方向
        std::string trendDirection;
        if (std::abs(trend.slope) < 0.001) {
            trendDirection = "stable";
        } else if (trend.slope > 0) {
            trendDirection = "degrading"; // 对于性能指标，增长通常意味着性能下降
        } else {
            trendDirection = "improving";
        }

        return {predictedValue, confidence, trendDirection, futureTime};
    }

private:
    void AnalyzeTrend(const std::string& metric) {
        auto& trend = _trends[metric];
        const auto& data = trend.timeSeriesData;

        if (data.size() < 2) return;

        // 线性回归分析
        double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
        size_t n = data.size();

        for (const auto& point : data) {
            double x = static_cast<double>(point.first);
            double y = point.second;
            sumX += x;
            sumY += y;
            sumXY += x * y;
            sumX2 += x * x;
        }

        double meanX = sumX / n;
        double meanY = sumY / n;

        // 计算斜率和截距
        double denominator = sumX2 - n * meanX * meanX;
        if (std::abs(denominator) > 1e-10) {
            trend.slope = (sumXY - n * meanX * meanY) / denominator;
            trend.intercept = meanY - trend.slope * meanX;

            // 计算相关系数
            double sumY2 = 0;
            for (const auto& point : data) {
                sumY2 += point.second * point.second;
            }

            double numerator = sumXY - n * meanX * meanY;
            double denomX = std::sqrt(sumX2 - n * meanX * meanX);
            double denomY = std::sqrt(sumY2 - n * meanY * meanY);

            if (denomX > 1e-10 && denomY > 1e-10) {
                trend.correlation = numerator / (denomX * denomY);
            }
        }
    }
};
```

#### 16.3.2 异常检测系统
```cpp
class AnomalyDetector {
private:
    struct BaselineData {
        double mean = 0.0;
        double stdDev = 0.0;
        std::vector<double> historicalValues;
        uint64 lastUpdate = 0;
        bool isInitialized = false;
    };

    std::unordered_map<std::string, BaselineData> _baselines;

public:
    enum class AnomalyLevel {
        NORMAL,
        MINOR,
        MAJOR,
        CRITICAL
    };

    struct AnomalyResult {
        AnomalyLevel level;
        double severity; // 0.0 - 1.0
        double zScore;
        std::string description;
    };

    void UpdateBaseline(const std::string& metric, double value) {
        auto& baseline = _baselines[metric];
        baseline.historicalValues.push_back(value);

        // 保持最近1000个样本用于基线计算
        if (baseline.historicalValues.size() > 1000) {
            baseline.historicalValues.erase(baseline.historicalValues.begin());
        }

        // 需要至少30个样本才能建立可靠的基线
        if (baseline.historicalValues.size() >= 30) {
            RecalculateBaseline(metric);
            baseline.isInitialized = true;
        }

        baseline.lastUpdate = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
    }

    AnomalyResult DetectAnomaly(const std::string& metric, double value) {
        auto it = _baselines.find(metric);
        if (it == _baselines.end() || !it->second.isInitialized) {
            return {AnomalyLevel::NORMAL, 0.0, 0.0, "Insufficient baseline data"};
        }

        const auto& baseline = it->second;

        // 计算Z-score
        double zScore = 0.0;
        if (baseline.stdDev > 1e-10) {
            zScore = (value - baseline.mean) / baseline.stdDev;
        }

        // 根据Z-score确定异常级别
        AnomalyLevel level = AnomalyLevel::NORMAL;
        double severity = 0.0;
        std::string description;

        double absZScore = std::abs(zScore);
        if (absZScore > 3.0) {
            level = AnomalyLevel::CRITICAL;
            severity = std::min(1.0, absZScore / 5.0);
            description = fmt::format("Critical anomaly: value {} is {:.2f} standard deviations from baseline {:.2f}",
                                    value, absZScore, baseline.mean);
        } else if (absZScore > 2.5) {
            level = AnomalyLevel::MAJOR;
            severity = absZScore / 3.0;
            description = fmt::format("Major anomaly: value {} is {:.2f} standard deviations from baseline {:.2f}",
                                    value, absZScore, baseline.mean);
        } else if (absZScore > 2.0) {
            level = AnomalyLevel::MINOR;
            severity = absZScore / 2.5;
            description = fmt::format("Minor anomaly: value {} is {:.2f} standard deviations from baseline {:.2f}",
                                    value, absZScore, baseline.mean);
        } else {
            description = "Normal value within expected range";
        }

        return {level, severity, zScore, description};
    }

    // 获取指标的健康状态
    struct HealthStatus {
        std::string metric;
        double currentValue;
        double baselineMean;
        double baselineStdDev;
        AnomalyLevel currentLevel;
        uint32 anomalyCount24h;
        double healthScore; // 0.0 - 1.0, 1.0 = perfect health
    };

    HealthStatus GetHealthStatus(const std::string& metric) {
        auto it = _baselines.find(metric);
        if (it == _baselines.end() || !it->second.isInitialized) {
            return {metric, 0.0, 0.0, 0.0, AnomalyLevel::NORMAL, 0, 0.0};
        }

        const auto& baseline = it->second;
        double currentValue = baseline.historicalValues.empty() ? 0.0 : baseline.historicalValues.back();

        auto anomalyResult = DetectAnomaly(metric, currentValue);

        // 计算24小时内的异常次数
        uint32 anomalyCount = CountRecentAnomalies(metric, 86400); // 24小时

        // 计算健康分数
        double healthScore = 1.0;
        if (anomalyResult.level != AnomalyLevel::NORMAL) {
            healthScore -= anomalyResult.severity * 0.5;
        }
        healthScore -= std::min(0.5, anomalyCount * 0.05); // 每次异常减少5%，最多减少50%
        healthScore = std::max(0.0, healthScore);

        return {
            metric,
            currentValue,
            baseline.mean,
            baseline.stdDev,
            anomalyResult.level,
            anomalyCount,
            healthScore
        };
    }

private:
    void RecalculateBaseline(const std::string& metric) {
        auto& baseline = _baselines[metric];
        const auto& values = baseline.historicalValues;

        if (values.empty()) return;

        // 计算均值
        double sum = std::accumulate(values.begin(), values.end(), 0.0);
        baseline.mean = sum / values.size();

        // 计算标准差
        double variance = 0.0;
        for (double value : values) {
            variance += std::pow(value - baseline.mean, 2);
        }
        baseline.stdDev = std::sqrt(variance / values.size());
    }

    uint32 CountRecentAnomalies(const std::string& metric, uint64 timeWindowSeconds) {
        // 这里需要维护一个异常历史记录
        // 为简化示例，返回模拟值
        return 0;
    }
};
```

### 16.4 实时优化建议系统

#### 16.4.1 自适应配置调整
```cpp
class AdaptiveConfigManager {
private:
    struct ConfigRule {
        std::string configKey;
        std::function<bool(const PerformanceMetrics&)> condition;
        std::function<std::string(const PerformanceMetrics&)> newValue;
        std::string description;
        uint32 cooldownSeconds;
        uint64 lastApplied;
    };

    std::vector<ConfigRule> _rules;

public:
    struct PerformanceMetrics {
        uint32 averageUpdateTime;
        uint32 maxUpdateTime;
        uint32 playerCount;
        uint32 activeMapCount;
        double cpuUsage;
        double memoryUsage;
        uint32 dbQueueSize;
    };

    void Initialize() {
        // 注册自适应规则
        RegisterRule({
            "MinWorldUpdateTime",
            [](const PerformanceMetrics& metrics) {
                return metrics.averageUpdateTime > 200 && metrics.cpuUsage > 80.0;
            },
            [](const PerformanceMetrics& metrics) {
                return std::to_string(std::min(50u, metrics.averageUpdateTime / 4));
            },
            "Increase MinWorldUpdateTime to reduce CPU pressure",
            300, // 5分钟冷却
            0
        });

        RegisterRule({
            "ThreadPool",
            [](const PerformanceMetrics& metrics) {
                return metrics.averageUpdateTime > 300 && metrics.activeMapCount > 10;
            },
            [](const PerformanceMetrics& metrics) {
                uint32 currentThreads = sConfigMgr->GetOption<uint32>("ThreadPool", 2);
                return std::to_string(std::min(8u, currentThreads + 1));
            },
            "Increase thread pool size for better map update performance",
            600, // 10分钟冷却
            0
        });

        RegisterRule({
            "RecordUpdateTimeDiffInterval",
            [](const PerformanceMetrics& metrics) {
                return metrics.averageUpdateTime > 500;
            },
            [](const PerformanceMetrics& metrics) {
                return "60000"; // 1分钟，更频繁的监控
            },
            "Increase monitoring frequency during performance issues",
            1800, // 30分钟冷却
            0
        });
    }

    std::vector<std::string> EvaluateAndApply(const PerformanceMetrics& metrics) {
        std::vector<std::string> appliedChanges;
        uint64 currentTime = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        for (auto& rule : _rules) {
            // 检查冷却时间
            if (currentTime - rule.lastApplied < rule.cooldownSeconds) {
                continue;
            }

            // 检查条件
            if (rule.condition(metrics)) {
                std::string newValue = rule.newValue(metrics);
                std::string currentValue = sConfigMgr->GetOption<std::string>(rule.configKey, "");

                if (newValue != currentValue) {
                    // 应用配置更改
                    ApplyConfigChange(rule.configKey, newValue);
                    rule.lastApplied = currentTime;

                    std::string changeDescription = fmt::format(
                        "Auto-adjusted {} from {} to {}: {}",
                        rule.configKey, currentValue, newValue, rule.description);

                    appliedChanges.push_back(changeDescription);

                    LOG_INFO("adaptive.config", "{}", changeDescription);
                    METRIC_EVENT("config_auto_adjustment", "Configuration Auto-Adjusted", changeDescription);
                }
            }
        }

        return appliedChanges;
    }

private:
    void RegisterRule(const ConfigRule& rule) {
        _rules.push_back(rule);
    }

    void ApplyConfigChange(const std::string& key, const std::string& value) {
        // 动态更新配置
        sConfigMgr->SetOption(key, value);

        // 通知相关系统重新加载配置
        if (key == "ThreadPool") {
            // 重新配置线程池（需要重启才能生效）
            LOG_WARN("adaptive.config", "ThreadPool change requires server restart to take effect");
        } else if (key == "MinWorldUpdateTime") {
            // 立即生效的配置
        }
    }
};
```

#### 16.4.2 负载均衡优化建议
```cpp
class LoadBalancingAdvisor {
public:
    struct LoadBalanceRecommendation {
        std::string type; // "player_distribution", "map_optimization", "resource_allocation"
        std::string description;
        uint32 priority; // 1-10, 10 = highest priority
        std::vector<std::string> actions;
        double expectedImprovement; // 预期性能提升百分比
    };

    std::vector<LoadBalanceRecommendation> AnalyzeAndRecommend(const PerformanceMetrics& metrics) {
        std::vector<LoadBalanceRecommendation> recommendations;

        // 分析玩家分布
        if (metrics.playerCount > 100) {
            auto playerDistribution = AnalyzePlayerDistribution();
            if (playerDistribution.maxPlayersInSingleMap > metrics.playerCount * 0.6) {
                recommendations.push_back({
                    "player_distribution",
                    "High player concentration detected in single map",
                    8,
                    {
                        "Consider implementing dynamic instance creation",
                        "Add incentives for players to spread across different zones",
                        "Implement queue system for overcrowded areas"
                    },
                    25.0
                });
            }
        }

        // 分析地图更新性能
        if (metrics.averageUpdateTime > 200) {
            recommendations.push_back({
                "map_optimization",
                "Map update performance degradation detected",
                9,
                {
                    "Enable multi-threaded map updates",
                    "Optimize creature AI update frequency",
                    "Implement map-specific update intervals",
                    "Consider map grid preloading for high-traffic areas"
                },
                35.0
            });
        }

        // 分析资源分配
        if (metrics.cpuUsage > 85.0 || metrics.memoryUsage > 90.0) {
            recommendations.push_back({
                "resource_allocation",
                "High resource utilization detected",
                10,
                {
                    "Increase server hardware resources",
                    "Implement more aggressive garbage collection",
                    "Optimize database connection pooling",
                    "Consider horizontal scaling"
                },
                40.0
            });
        }

        // 按优先级排序
        std::sort(recommendations.begin(), recommendations.end(),
                 [](const auto& a, const auto& b) { return a.priority > b.priority; });

        return recommendations;
    }

private:
    struct PlayerDistributionData {
        uint32 totalPlayers;
        uint32 maxPlayersInSingleMap;
        std::string mostCrowdedMap;
        double distributionVariance;
    };

    PlayerDistributionData AnalyzePlayerDistribution() {
        // 实际实现需要遍历所有地图统计玩家数量
        // 这里返回模拟数据
        return {150, 95, "Stormwind City", 0.8};
    }
};
```

### 16.5 分布式监控系统

#### 16.5.1 多服务器集群监控
```cpp
class ClusterMonitor {
private:
    struct ServerNode {
        std::string serverId;
        std::string hostname;
        uint16 port;
        uint64 lastHeartbeat;
        PerformanceMetrics metrics;
        bool isOnline;
    };

    std::unordered_map<std::string, ServerNode> _nodes;
    std::string _localServerId;

public:
    void Initialize(const std::string& serverId) {
        _localServerId = serverId;

        // 启动心跳发送
        StartHeartbeatSender();

        // 启动节点发现
        StartNodeDiscovery();
    }

    void RegisterNode(const std::string& serverId, const std::string& hostname, uint16 port) {
        _nodes[serverId] = {serverId, hostname, port, 0, {}, false};
    }

    void UpdateLocalMetrics(const PerformanceMetrics& metrics) {
        if (_nodes.find(_localServerId) != _nodes.end()) {
            _nodes[_localServerId].metrics = metrics;
            _nodes[_localServerId].lastHeartbeat = GetCurrentTimestamp();
            _nodes[_localServerId].isOnline = true;
        }

        // 广播到其他节点
        BroadcastMetrics(metrics);
    }

    struct ClusterHealth {
        uint32 totalNodes;
        uint32 onlineNodes;
        uint32 totalPlayers;
        double averageUpdateTime;
        double maxUpdateTime;
        std::string bottleneckServer;
        std::vector<std::string> offlineServers;
    };

    ClusterHealth GetClusterHealth() {
        ClusterHealth health = {};
        health.totalNodes = _nodes.size();

        double totalUpdateTime = 0.0;
        uint32 onlineCount = 0;

        for (const auto& [serverId, node] : _nodes) {
            if (IsNodeOnline(node)) {
                health.onlineNodes++;
                health.totalPlayers += node.metrics.playerCount;
                totalUpdateTime += node.metrics.averageUpdateTime;
                onlineCount++;

                if (node.metrics.averageUpdateTime > health.maxUpdateTime) {
                    health.maxUpdateTime = node.metrics.averageUpdateTime;
                    health.bottleneckServer = serverId;
                }
            } else {
                health.offlineServers.push_back(serverId);
            }
        }

        if (onlineCount > 0) {
            health.averageUpdateTime = totalUpdateTime / onlineCount;
        }

        return health;
    }

    // 跨服务器性能对比
    std::vector<std::pair<std::string, double>> GetPerformanceRanking() {
        std::vector<std::pair<std::string, double>> ranking;

        for (const auto& [serverId, node] : _nodes) {
            if (IsNodeOnline(node)) {
                // 计算性能分数（越低越好）
                double score = node.metrics.averageUpdateTime * 0.6 +
                              node.metrics.maxUpdateTime * 0.3 +
                              (node.metrics.playerCount / 100.0) * 0.1;
                ranking.emplace_back(serverId, score);
            }
        }

        std::sort(ranking.begin(), ranking.end(),
                 [](const auto& a, const auto& b) { return a.second < b.second; });

        return ranking;
    }

private:
    uint64 GetCurrentTimestamp() {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
    }

    bool IsNodeOnline(const ServerNode& node) {
        uint64 currentTime = GetCurrentTimestamp();
        return (currentTime - node.lastHeartbeat) < 60; // 60秒超时
    }

    void StartHeartbeatSender() {
        // 实现心跳发送逻辑
        // 每30秒向其他节点发送心跳
    }

    void StartNodeDiscovery() {
        // 实现节点发现逻辑
        // 可以使用UDP广播、配置文件或服务注册中心
    }

    void BroadcastMetrics(const PerformanceMetrics& metrics) {
        // 向其他节点广播性能指标
        for (const auto& [serverId, node] : _nodes) {
            if (serverId != _localServerId && IsNodeOnline(node)) {
                SendMetricsToNode(node, metrics);
            }
        }
    }

    void SendMetricsToNode(const ServerNode& node, const PerformanceMetrics& metrics) {
        // 实现网络通信逻辑
        // 可以使用HTTP REST API、gRPC或自定义协议
    }
};
```

#### 16.5.2 全局性能统计和报告
```cpp
class GlobalPerformanceReporter {
public:
    struct GlobalReport {
        uint64 reportTimestamp;
        ClusterMonitor::ClusterHealth clusterHealth;
        std::vector<std::pair<std::string, double>> serverRanking;
        std::vector<LoadBalancingAdvisor::LoadBalanceRecommendation> recommendations;
        std::map<std::string, AnomalyDetector::HealthStatus> healthStatuses;
        std::string summary;
    };

    GlobalReport GenerateReport(ClusterMonitor& clusterMonitor,
                               LoadBalancingAdvisor& advisor,
                               AnomalyDetector& anomalyDetector) {
        GlobalReport report;
        report.reportTimestamp = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        // 收集集群健康状态
        report.clusterHealth = clusterMonitor.GetClusterHealth();

        // 获取服务器性能排名
        report.serverRanking = clusterMonitor.GetPerformanceRanking();

        // 生成优化建议
        PerformanceMetrics avgMetrics = CalculateAverageMetrics(report.clusterHealth);
        report.recommendations = advisor.AnalyzeAndRecommend(avgMetrics);

        // 收集健康状态
        std::vector<std::string> keyMetrics = {
            "world_update_time", "player_count", "memory_usage", "cpu_usage"
        };
        for (const auto& metric : keyMetrics) {
            report.healthStatuses[metric] = anomalyDetector.GetHealthStatus(metric);
        }

        // 生成摘要
        report.summary = GenerateSummary(report);

        return report;
    }

    void ExportToJson(const GlobalReport& report, const std::string& filename) {
        nlohmann::json j;

        j["timestamp"] = report.reportTimestamp;
        j["cluster_health"] = {
            {"total_nodes", report.clusterHealth.totalNodes},
            {"online_nodes", report.clusterHealth.onlineNodes},
            {"total_players", report.clusterHealth.totalPlayers},
            {"average_update_time", report.clusterHealth.averageUpdateTime},
            {"max_update_time", report.clusterHealth.maxUpdateTime},
            {"bottleneck_server", report.clusterHealth.bottleneckServer}
        };

        j["server_ranking"] = nlohmann::json::array();
        for (const auto& [server, score] : report.serverRanking) {
            j["server_ranking"].push_back({{"server", server}, {"score", score}});
        }

        j["recommendations"] = nlohmann::json::array();
        for (const auto& rec : report.recommendations) {
            j["recommendations"].push_back({
                {"type", rec.type},
                {"description", rec.description},
                {"priority", rec.priority},
                {"actions", rec.actions},
                {"expected_improvement", rec.expectedImprovement}
            });
        }

        j["summary"] = report.summary;

        std::ofstream file(filename);
        file << j.dump(4);
    }

private:
    PerformanceMetrics CalculateAverageMetrics(const ClusterMonitor::ClusterHealth& health) {
        return {
            static_cast<uint32>(health.averageUpdateTime),
            static_cast<uint32>(health.maxUpdateTime),
            health.totalPlayers,
            0, // activeMapCount - 需要从集群数据计算
            0.0, // cpuUsage - 需要从各节点收集
            0.0, // memoryUsage - 需要从各节点收集
            0 // dbQueueSize - 需要从各节点收集
        };
    }

    std::string GenerateSummary(const GlobalReport& report) {
        std::stringstream ss;

        ss << "=== AzerothCore Cluster Performance Report ===\n";
        ss << "Generated at: " << std::ctime(reinterpret_cast<const time_t*>(&report.reportTimestamp));
        ss << "\nCluster Status:\n";
        ss << "- Nodes: " << report.clusterHealth.onlineNodes << "/" << report.clusterHealth.totalNodes << " online\n";
        ss << "- Total Players: " << report.clusterHealth.totalPlayers << "\n";
        ss << "- Average Update Time: " << report.clusterHealth.averageUpdateTime << "ms\n";

        if (report.clusterHealth.averageUpdateTime > 300) {
            ss << "⚠️  WARNING: High average update time detected\n";
        } else if (report.clusterHealth.averageUpdateTime < 100) {
            ss << "✅ GOOD: Excellent performance across cluster\n";
        }

        if (!report.clusterHealth.bottleneckServer.empty()) {
            ss << "🔍 Bottleneck Server: " << report.clusterHealth.bottleneckServer << "\n";
        }

        if (!report.recommendations.empty()) {
            ss << "\nTop Recommendations:\n";
            for (size_t i = 0; i < std::min(size_t(3), report.recommendations.size()); ++i) {
                const auto& rec = report.recommendations[i];
                ss << "- " << rec.description << " (Priority: " << rec.priority << "/10)\n";
            }
        }

        return ss.str();
    }
};
```

## 17. 实施指南和最佳实践

### 17.1 分阶段实施计划

#### 阶段1：基础监控增强（1-2周）
```cpp
// 1. 扩展现有UpdateTime类
class EnhancedWorldUpdateTime : public WorldUpdateTime {
    // 添加组件级监控
    // 实现实时告警
    // 增强百分位数统计
};

// 2. 集成到现有系统
void World::Update(uint32 diff) {
    METRIC_TIMER("world_update_time_total");

    // 现有代码...
    sEnhancedWorldUpdateTime.UpdateWithDiff(diff);

    // 添加组件级监控
    {
        METRIC_TIMER("world_update_component", METRIC_TAG("component", "sessions"));
        sWorldSessionMgr->UpdateSessions(diff);
        sEnhancedWorldUpdateTime.UpdateComponentTime("sessions",
            /* 获取实际执行时间 */);
    }

    // 其他组件类似处理...
}
```

#### 阶段2：外部系统集成（2-3周）
```cpp
// 1. 配置Prometheus导出器
PrometheusExporter prometheusExporter;
prometheusExporter.Initialize("/metrics");

// 2. 配置Grafana仪表板
// 使用提供的JSON配置创建监控面板

// 3. 设置告警规则
PerformanceAlertManager alertManager;
alertManager.RegisterThreshold("world_update_time", 300, 600, 300);
```

#### 阶段3：智能分析功能（3-4周）
```cpp
// 1. 部署趋势分析
PerformanceTrendAnalyzer trendAnalyzer;
AnomalyDetector anomalyDetector;

// 2. 集成到监控循环
void MonitoringLoop() {
    auto metrics = CollectCurrentMetrics();

    trendAnalyzer.AddDataPoint("world_update_time", metrics.averageUpdateTime);
    auto prediction = trendAnalyzer.PredictValue("world_update_time", 3600); // 1小时后预测

    auto anomaly = anomalyDetector.DetectAnomaly("world_update_time", metrics.averageUpdateTime);
    if (anomaly.level != AnomalyDetector::AnomalyLevel::NORMAL) {
        HandleAnomaly(anomaly);
    }
}
```

#### 阶段4：自动化优化（4-5周）
```cpp
// 1. 启用自适应配置
AdaptiveConfigManager configManager;
configManager.Initialize();

// 2. 定期评估和应用优化
void PerformanceOptimizationTask() {
    auto metrics = CollectCurrentMetrics();
    auto changes = configManager.EvaluateAndApply(metrics);

    for (const auto& change : changes) {
        LOG_INFO("optimization", "Applied: {}", change);
        NotifyAdministrators(change);
    }
}
```

#### 阶段5：集群监控（5-6周）
```cpp
// 1. 部署集群监控
ClusterMonitor clusterMonitor;
clusterMonitor.Initialize(GetServerIdentifier());

// 2. 配置节点发现
// 在配置文件中添加其他服务器信息
// 或实现自动发现机制

// 3. 生成全局报告
GlobalPerformanceReporter reporter;
auto report = reporter.GenerateReport(clusterMonitor, advisor, anomalyDetector);
reporter.ExportToJson(report, "performance_report.json");
```

### 17.2 配置文件扩展

#### worldserver.conf 新增配置项
```ini
###################################################################################################
# ENHANCED PERFORMANCE MONITORING
#

#
#    EnhancedMonitoring.Enable
#        Description: Enable enhanced performance monitoring features
#        Default:     1 - (Enabled)
#                     0 - (Disabled)

EnhancedMonitoring.Enable = 1

#
#    EnhancedMonitoring.ComponentTracking
#        Description: Enable detailed component-level performance tracking
#        Default:     1 - (Enabled)
#                     0 - (Disabled)

EnhancedMonitoring.ComponentTracking = 1

#
#    PerformanceAlert.Enable
#        Description: Enable automatic performance alerting
#        Default:     1 - (Enabled)
#                     0 - (Disabled)

PerformanceAlert.Enable = 1

#
#    PerformanceAlert.UpdateTimeWarning
#        Description: Warning threshold for world update time (milliseconds)
#        Default:     300

PerformanceAlert.UpdateTimeWarning = 300

#
#    PerformanceAlert.UpdateTimeCritical
#        Description: Critical threshold for world update time (milliseconds)
#        Default:     600

PerformanceAlert.UpdateTimeCritical = 600

#
#    AdaptiveConfig.Enable
#        Description: Enable automatic configuration adjustments
#        Default:     0 - (Disabled, recommended for production)
#                     1 - (Enabled, use with caution)

AdaptiveConfig.Enable = 0

#
#    ClusterMonitoring.Enable
#        Description: Enable cluster-wide monitoring
#        Default:     0 - (Disabled)
#                     1 - (Enabled)

ClusterMonitoring.Enable = 0

#
#    ClusterMonitoring.ServerId
#        Description: Unique identifier for this server in the cluster
#        Default:     "server1"

ClusterMonitoring.ServerId = "server1"

#
#    ClusterMonitoring.Nodes
#        Description: List of other cluster nodes (format: id:hostname:port,...)
#        Default:     ""

ClusterMonitoring.Nodes = "server2:*************:8080,server3:*************:8080"

#
#    Prometheus.Enable
#        Description: Enable Prometheus metrics export
#        Default:     0 - (Disabled)
#                     1 - (Enabled)

Prometheus.Enable = 0

#
#    Prometheus.Port
#        Description: Port for Prometheus metrics endpoint
#        Default:     9090

Prometheus.Port = 9090
```

### 17.3 部署和维护建议

#### 17.3.1 监控数据存储
```sql
-- 创建性能监控数据表
CREATE TABLE performance_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    server_id VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DOUBLE NOT NULL,
    timestamp BIGINT NOT NULL,
    tags JSON,
    INDEX idx_server_metric_time (server_id, metric_name, timestamp),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB;

-- 创建异常记录表
CREATE TABLE performance_anomalies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    server_id VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    anomaly_level ENUM('MINOR', 'MAJOR', 'CRITICAL') NOT NULL,
    severity DOUBLE NOT NULL,
    z_score DOUBLE NOT NULL,
    description TEXT,
    timestamp BIGINT NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    INDEX idx_server_time (server_id, timestamp),
    INDEX idx_level_resolved (anomaly_level, resolved)
) ENGINE=InnoDB;
```

#### 17.3.2 维护脚本
```bash
#!/bin/bash
# performance_maintenance.sh - 性能监控系统维护脚本

# 清理过期的性能数据（保留30天）
mysql -u$DB_USER -p$DB_PASS $DB_NAME << EOF
DELETE FROM performance_metrics
WHERE timestamp < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY)) * 1000;

DELETE FROM performance_anomalies
WHERE timestamp < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 90 DAY)) * 1000;
EOF

# 生成每日性能报告
./generate_daily_report.sh

# 检查监控系统健康状态
./check_monitoring_health.sh

echo "Performance monitoring maintenance completed at $(date)"
```

## 18. 总结

通过本文档提供的全面扩展方案，AzerothCore的性能监控系统可以从基础的`_averageUpdateTime`统计发展为企业级的智能监控平台。主要改进包括：

### 18.1 核心改进
1. **细粒度监控**: 从单一指标扩展到组件级性能追踪
2. **智能分析**: 集成趋势预测和异常检测算法
3. **自动化优化**: 实现配置自适应调整和负载均衡建议
4. **集群支持**: 支持多服务器环境的统一监控
5. **外部集成**: 与Prometheus、Grafana等主流监控工具集成

### 18.2 实施价值
- **提升运维效率**: 自动化监控和告警减少人工干预
- **预防性维护**: 趋势分析和异常检测提前发现问题
- **性能优化**: 数据驱动的优化建议提升服务器性能
- **可扩展性**: 支持从单服务器到大型集群的监控需求

### 18.3 技术特色
- **非侵入式设计**: 最小化对现有代码的修改
- **模块化架构**: 各功能模块可独立部署和升级
- **高性能实现**: 监控系统本身对服务器性能影响极小
- **企业级特性**: 支持高可用、负载均衡和故障恢复

这个扩展方案为AzerothCore提供了从基础监控到智能运维的完整解决方案，为服务器的稳定运行和性能优化提供了强有力的技术支撑。
```
```
```
